import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'driver_kyc_screen.dart';

class DriverRegisterScreen extends StatefulWidget {
  final String phoneNumber;

  const DriverRegisterScreen({
    super.key,
    required this.phoneNumber,
  });

  @override
  State<DriverRegisterScreen> createState() => _DriverRegisterScreenState();
}

class _DriverRegisterScreenState extends State<DriverRegisterScreen> {
  final _formKey = GlobalKey<FormState>();

  // Personal Information Controllers
  final _fullNameController = TextEditingController();
  final _ageController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();

  // Location Details Controllers
  final _countryController = TextEditingController();
  final _stateController = TextEditingController();
  final _districtController = TextEditingController();
  final _mandalController = TextEditingController();
  final _cityController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _warehouseMemberController = TextEditingController();
  final _siteController = TextEditingController();

  // Address Information Controllers
  final _addressController = TextEditingController();

  // Emergency Contact Controllers
  final _emergencyContactNameController = TextEditingController();
  final _emergencyContactPhoneController = TextEditingController();

  // Dropdown selections
  String? _selectedGender;
  String? _selectedCountry;
  String? _selectedState;
  String? _selectedDistrict;
  bool _sameAsCurrentAddress = false;
  bool _termsAccepted = false;
  bool _whatsappUpdates = false;

  // Image picker
  File? _profileImage;
  final ImagePicker _picker = ImagePicker();

  final List<String> _genderOptions = [
    "Male",
    "Female",
    "Other",
  ];

  // Country-State-City data
  final Map<String, List<String>> _countryStates = {
    "India": [
      "Andhra Pradesh",
      "Karnataka",
      "Tamil Nadu",
      "Telangana",
      "Kerala",
      "Maharashtra",
      "Gujarat"
    ],
    "USA": ["California", "Texas", "Florida", "New York", "Illinois"],
    "UK": ["England", "Scotland", "Wales", "Northern Ireland"],
  };

  final Map<String, List<String>> _stateDistricts = {
    "Andhra Pradesh": [
      "Anantapur",
      "Chittoor",
      "East Godavari",
      "Guntur",
      "Krishna",
      "Kurnool",
      "Nellore",
      "Prakasam",
      "Srikakulam",
      "Visakhapatnam",
      "Vizianagaram",
      "West Godavari",
      "YSR Kadapa"
    ],
    "Karnataka": [
      "Bagalkot",
      "Bangalore Rural",
      "Bangalore Urban",
      "Belgaum",
      "Bellary",
      "Bidar",
      "Chamarajanagar",
      "Chikkaballapur",
      "Chikkamagaluru",
      "Chitradurga",
      "Dakshina Kannada",
      "Davanagere",
      "Dharwad",
      "Gadag",
      "Gulbarga",
      "Hassan",
      "Haveri",
      "Kodagu",
      "Kolar",
      "Koppal",
      "Mandya",
      "Mysore",
      "Raichur",
      "Ramanagara",
      "Shimoga",
      "Tumkur",
      "Udupi",
      "Uttara Kannada",
      "Yadgir"
    ],
    "Tamil Nadu": [
      "Ariyalur",
      "Chennai",
      "Coimbatore",
      "Cuddalore",
      "Dharmapuri",
      "Dindigul",
      "Erode",
      "Kanchipuram",
      "Kanyakumari",
      "Karur",
      "Krishnagiri",
      "Madurai",
      "Nagapattinam",
      "Namakkal",
      "Nilgiris",
      "Perambalur",
      "Pudukkottai",
      "Ramanathapuram",
      "Salem",
      "Sivaganga",
      "Thanjavur",
      "Theni",
      "Thoothukudi",
      "Tiruchirappalli",
      "Tirunelveli",
      "Tiruppur",
      "Tiruvallur",
      "Tiruvannamalai",
      "Tiruvarur",
      "Vellore",
      "Viluppuram",
      "Virudhunagar"
    ],
    "Telangana": [
      "Adilabad",
      "Bhadradri Kothagudem",
      "Hyderabad",
      "Jagtial",
      "Jangaon",
      "Jayashankar",
      "Jogulamba",
      "Kamareddy",
      "Karimnagar",
      "Khammam",
      "Komaram Bheem",
      "Mahabubabad",
      "Mahabubnagar",
      "Mancherial",
      "Medak",
      "Medchal",
      "Nagarkurnool",
      "Nalgonda",
      "Nirmal",
      "Nizamabad",
      "Peddapalli",
      "Rajanna Sircilla",
      "Rangareddy",
      "Sangareddy",
      "Siddipet",
      "Suryapet",
      "Vikarabad",
      "Wanaparthy",
      "Warangal Rural",
      "Warangal Urban",
      "Yadadri Bhuvanagiri"
    ],
    "Kerala": [
      "Alappuzha",
      "Ernakulam",
      "Idukki",
      "Kannur",
      "Kasaragod",
      "Kollam",
      "Kottayam",
      "Kozhikode",
      "Malappuram",
      "Palakkad",
      "Pathanamthitta",
      "Thiruvananthapuram",
      "Thrissur",
      "Wayanad"
    ],
    "Maharashtra": [
      "Ahmednagar",
      "Akola",
      "Amravati",
      "Aurangabad",
      "Beed",
      "Bhandara",
      "Buldhana",
      "Chandrapur",
      "Dhule",
      "Gadchiroli",
      "Gondia",
      "Hingoli",
      "Jalgaon",
      "Jalna",
      "Kolhapur",
      "Latur",
      "Mumbai City",
      "Mumbai Suburban",
      "Nagpur",
      "Nanded",
      "Nandurbar",
      "Nashik",
      "Osmanabad",
      "Palghar",
      "Parbhani",
      "Pune",
      "Raigad",
      "Ratnagiri",
      "Sangli",
      "Satara",
      "Sindhudurg",
      "Solapur",
      "Thane",
      "Wardha",
      "Washim",
      "Yavatmal"
    ],
    "Gujarat": [
      "Ahmedabad",
      "Amreli",
      "Anand",
      "Aravalli",
      "Banaskantha",
      "Bharuch",
      "Bhavnagar",
      "Botad",
      "Chhota Udaipur",
      "Dahod",
      "Dang",
      "Devbhoomi Dwarka",
      "Gandhinagar",
      "Gir Somnath",
      "Jamnagar",
      "Junagadh",
      "Kachchh",
      "Kheda",
      "Mahisagar",
      "Mehsana",
      "Morbi",
      "Narmada",
      "Navsari",
      "Panchmahal",
      "Patan",
      "Porbandar",
      "Rajkot",
      "Sabarkantha",
      "Surat",
      "Surendranagar",
      "Tapi",
      "Vadodara",
      "Valsad"
    ]
  };

  @override
  void initState() {
    super.initState();
    _phoneController.text = widget.phoneNumber;
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _profileImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _submitRegistration() {
    if (_formKey.currentState!.validate() && _termsAccepted) {
      String driverName = _fullNameController.text.trim();

      // Navigate to Driver KYC screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => DriverKYCScreen(
            driverName: driverName,
            phoneNumber: _phoneController.text,
          ),
        ),
      );
    } else if (!_termsAccepted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please accept the terms and conditions'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF002395), // Nikkou brand blue
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Header Section
                _buildHeader(),
                const SizedBox(height: 15),

                // Driver Details Title
                const Text(
                  "Driver details",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 15),

                // Profile Photo Upload Section
                _buildProfilePhotoSection(),
                const SizedBox(height: 15),

                // Driver Details Form
                _buildDriverDetailsForm(),
                const SizedBox(height: 15),

                // Location Details Section
                _buildLocationDetailsSection(),
                const SizedBox(height: 15),

                // Address Information Section
                _buildAddressInformationSection(),
                const SizedBox(height: 15),

                // Emergency Contact Section
                _buildEmergencyContactSection(),
                const SizedBox(height: 15),

                // Terms and Conditions
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Checkbox(
                        value: _termsAccepted,
                        activeColor: const Color(0xFF002395),
                        onChanged: (val) {
                          setState(() {
                            _termsAccepted = val ?? false;
                          });
                        },
                      ),
                      const Expanded(
                        child: Text(
                          "I accept the Terms and Conditions and Privacy Policy",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 15),

                // Register Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFFA726), // Orange button
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 2,
                    ),
                    onPressed: _submitRegistration,
                    child: const Text(
                      "Submit Details",
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white),
                    ),
                  ),
                ),

                const SizedBox(height: 10),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Image.asset(
          "assets/images/logo.png",
          height: 50,
        ),
        const SizedBox(height: 8),
        const Text(
          "NIKKOU",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            letterSpacing: 1.2,
          ),
        ),
        const Text(
          "Driver Registration",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          "Complete Your Profile",
          style: TextStyle(
            fontSize: 12,
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildProfilePhotoSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: _profileImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _profileImage!,
                      width: 100,
                      height: 100,
                      fit: BoxFit.cover,
                    ),
                  )
                : const Icon(
                    Icons.camera_alt,
                    size: 40,
                    color: Colors.grey,
                  ),
          ),
          const SizedBox(height: 12),
          const Text(
            "Upload your profile photo",
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: _pickImage,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFFA726),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            ),
            child: const Text(
              "Choose Photo",
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDriverDetailsForm() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextField("Full Name *", _fullNameController, isRequired: true),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildTextField("Age *", _ageController,
                    keyboardType: TextInputType.number, isRequired: true),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDropdown(
                    "Gender *",
                    _selectedGender,
                    _genderOptions,
                    (value) => setState(() => _selectedGender = value)),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTextField("Phone Number *", _phoneController,
              keyboardType: TextInputType.phone, isRequired: true),
          const SizedBox(height: 16),
          _buildTextField("Email", _emailController,
              keyboardType: TextInputType.emailAddress),
        ],
      ),
    );
  }

  Widget _buildLocationDetailsSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Location Details",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDropdown(
                  "Country",
                  _selectedCountry,
                  _countryStates.keys.toList(),
                  (value) {
                    setState(() {
                      _selectedCountry = value;
                      _selectedState = null; // Reset state when country changes
                      _selectedDistrict =
                          null; // Reset district when country changes
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDropdown(
                  "State",
                  _selectedState,
                  _selectedCountry != null
                      ? _countryStates[_selectedCountry!] ?? []
                      : [],
                  (value) {
                    setState(() {
                      _selectedState = value;
                      _selectedDistrict =
                          null; // Reset district when state changes
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDropdown(
                  "District",
                  _selectedDistrict,
                  _selectedState != null
                      ? _stateDistricts[_selectedState!] ?? []
                      : [],
                  (value) {
                    setState(() {
                      _selectedDistrict = value;
                      _districtController.text = value ?? "";
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildTextField("Mandal", _mandalController),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildTextField("City", _cityController),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildTextField("Pincode", _pincodeController,
                    keyboardType: TextInputType.number),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTextField("Warehouse Member", _warehouseMemberController),
          const SizedBox(height: 16),
          _buildTextField("Site", _siteController),
        ],
      ),
    );
  }

  Widget _buildAddressInformationSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Address Information",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Checkbox(
                value: _sameAsCurrentAddress,
                onChanged: (value) {
                  setState(() {
                    _sameAsCurrentAddress = value ?? false;
                  });
                },
                activeColor: const Color(0xFF002395),
              ),
              const Expanded(
                child: Text(
                  "Same as Current Address",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTextField("Address as Residential Address", _addressController,
              maxLines: 3),
        ],
      ),
    );
  }

  Widget _buildEmergencyContactSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Emergency Contact",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildTextField(
              "Emergency Contact Name", _emergencyContactNameController),
          const SizedBox(height: 16),
          _buildTextField(
              "Emergency Contact Phone", _emergencyContactPhoneController,
              keyboardType: TextInputType.phone),
        ],
      ),
    );
  }

  Widget _buildTextField(
    String hint,
    TextEditingController controller, {
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    int? maxLength,
    bool isRequired = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLines,
        maxLength: maxLength,
        style: const TextStyle(color: Colors.black87, fontSize: 14),
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: hint,
          hintStyle: const TextStyle(color: Colors.black54, fontSize: 14),
          counterText: '',
        ),
        validator: isRequired
            ? (value) {
                if (value == null || value.trim().isEmpty) {
                  return '$hint is required';
                }
                if (hint.contains("Email") &&
                    !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(value)) {
                  return 'Please enter a valid email';
                }
                if (hint.contains("Age")) {
                  int? age = int.tryParse(value);
                  if (age == null || age < 18 || age > 65) {
                    return 'Please enter a valid age (18-65)';
                  }
                }
                return null;
              }
            : null,
      ),
    );
  }

  Widget _buildDropdown(
    String hint,
    String? selectedValue,
    List<String> items,
    Function(String?) onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: DropdownButtonFormField<String>(
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 4, horizontal: 0),
          isDense: true,
        ),
        dropdownColor: Colors.white,
        isExpanded: true,
        icon: const Icon(Icons.arrow_drop_down, size: 16),
        hint: Text(
          hint,
          style: const TextStyle(color: Colors.black54, fontSize: 11),
          overflow: TextOverflow.ellipsis,
        ),
        initialValue: selectedValue,
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: const TextStyle(color: Colors.black87, fontSize: 11),
              overflow: TextOverflow.ellipsis,
            ),
          );
        }).toList(),
        onChanged: onChanged,
        validator: hint.contains('*')
            ? (value) {
                if (value == null || value.isEmpty) {
                  return '$hint is required';
                }
                return null;
              }
            : null,
      ),
    );
  }

  @override
  void dispose() {
    // Personal Information Controllers
    _fullNameController.dispose();
    _ageController.dispose();
    _phoneController.dispose();
    _emailController.dispose();

    // Location Details Controllers
    _countryController.dispose();
    _stateController.dispose();
    _districtController.dispose();
    _mandalController.dispose();
    _cityController.dispose();
    _pincodeController.dispose();
    _warehouseMemberController.dispose();
    _siteController.dispose();

    // Address Information Controllers
    _addressController.dispose();

    // Emergency Contact Controllers
    _emergencyContactNameController.dispose();
    _emergencyContactPhoneController.dispose();

    super.dispose();
  }
}
