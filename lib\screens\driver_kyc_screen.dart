import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'driver_documents_screen.dart';

class DriverKYCScreen extends StatefulWidget {
  final String driverName;
  final String phoneNumber;

  const DriverKYCScreen({
    super.key,
    required this.driverName,
    required this.phoneNumber,
  });

  @override
  State<DriverKYCScreen> createState() => _DriverKYCScreenState();
}

class _DriverKYCScreenState extends State<DriverKYCScreen> {
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();

  // Driving License Controllers
  final _licenseNumberController = TextEditingController();
  final _licenseExpiryController = TextEditingController();

  // Insurance Controllers
  final _policyNumberController = TextEditingController();
  final _insuranceExpiryController = TextEditingController();
  final _insuranceAmountController = TextEditingController();

  // Aadhar Controllers
  final _aadharNumberController = TextEditingController();

  // Image files
  File? _licenseFrontImage;
  File? _licenseBackImage;
  File? _insuranceDocImage;
  File? _aadharFrontImage;
  File? _aadharBackImage;

  Future<void> _pickImage(String imageType) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          switch (imageType) {
            case 'license_front':
              _licenseFrontImage = File(image.path);
              break;
            case 'license_back':
              _licenseBackImage = File(image.path);
              break;
            case 'insurance_doc':
              _insuranceDocImage = File(image.path);
              break;
            case 'aadhar_front':
              _aadharFrontImage = File(image.path);
              break;
            case 'aadhar_back':
              _aadharBackImage = File(image.path);
              break;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectDate(TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      setState(() {
        controller.text = "${picked.day}/${picked.month}/${picked.year}";
      });
    }
  }

  void _submitKYC() {
    if (_formKey.currentState!.validate()) {
      // Navigate to documents screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => DriverDocumentsScreen(
            driverName: widget.driverName,
            phoneNumber: widget.phoneNumber,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF002395),
      appBar: AppBar(
        backgroundColor: const Color(0xFF002395),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Driver KYC Management',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                // Driving License Section
                _buildDrivingLicenseSection(),
                const SizedBox(height: 20),

                // Insurance Details Section
                _buildInsuranceDetailsSection(),
                const SizedBox(height: 20),

                // Aadhar Card Details Section
                _buildAadharCardDetailsSection(),
                const SizedBox(height: 30),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFFA726),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 2,
                    ),
                    onPressed: _submitKYC,
                    child: const Text(
                      "Submit",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDrivingLicenseSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFA726),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(
                  Icons.credit_card,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                "Driving License",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildTextField("License Number", _licenseNumberController,
              isRequired: true),
          const SizedBox(height: 16),
          _buildDateField("Expiry Date", _licenseExpiryController),
          const SizedBox(height: 16),
          _buildImageUploadField(
              "Front Side Image", _licenseFrontImage, 'license_front'),
          const SizedBox(height: 16),
          _buildImageUploadField(
              "Back Side Image", _licenseBackImage, 'license_back'),
        ],
      ),
    );
  }

  Widget _buildInsuranceDetailsSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFA726),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(
                  Icons.security,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                "Insurance Details",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildTextField("Policy Number", _policyNumberController,
              isRequired: true),
          const SizedBox(height: 16),
          _buildDateField("Expiry Date", _insuranceExpiryController),
          const SizedBox(height: 16),
          _buildImageUploadField(
              "Insurance Document", _insuranceDocImage, 'insurance_doc'),
          const SizedBox(height: 16),
          _buildTextField("Insurance Amount", _insuranceAmountController,
              keyboardType: TextInputType.number, isRequired: true),
        ],
      ),
    );
  }

  Widget _buildAadharCardDetailsSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFA726),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Icon(
                  Icons.credit_card_outlined,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                "Aadhar Card Details",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildTextField("Aadhar Number", _aadharNumberController,
              keyboardType: TextInputType.number,
              maxLength: 12,
              isRequired: true),
          const SizedBox(height: 16),
          _buildImageUploadField(
              "Front Side Image", _aadharFrontImage, 'aadhar_front'),
          const SizedBox(height: 16),
          _buildImageUploadField(
              "Back Side Image", _aadharBackImage, 'aadhar_back'),
        ],
      ),
    );
  }

  Widget _buildTextField(
    String hint,
    TextEditingController controller, {
    TextInputType keyboardType = TextInputType.text,
    int? maxLength,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          hint,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: TextFormField(
            controller: controller,
            keyboardType: keyboardType,
            maxLength: maxLength,
            style: const TextStyle(color: Colors.black87, fontSize: 14),
            decoration: const InputDecoration(
              border: InputBorder.none,
              counterText: '',
            ),
            validator: isRequired
                ? (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '$hint is required';
                    }
                    if (hint.contains("Aadhar") && value.length != 12) {
                      return 'Aadhar number must be 12 digits';
                    }
                    return null;
                  }
                : null,
          ),
        ),
      ],
    );
  }

  Widget _buildDateField(String hint, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          hint,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: TextFormField(
            controller: controller,
            readOnly: true,
            style: const TextStyle(color: Colors.black87, fontSize: 14),
            decoration: const InputDecoration(
              border: InputBorder.none,
              suffixIcon: Icon(Icons.calendar_today, size: 20),
            ),
            onTap: () => _selectDate(controller),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '$hint is required';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildImageUploadField(
      String hint, File? imageFile, String imageType) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          hint,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: imageFile != null
              ? Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.file(
                        imageFile,
                        width: double.infinity,
                        height: 120,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: GestureDetector(
                        onTap: () => _pickImage(imageType),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.black54,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.edit,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              : InkWell(
                  onTap: () => _pickImage(imageType),
                  child: Container(
                    width: double.infinity,
                    height: 120,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.grey[300]!,
                        style: BorderStyle.solid,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFA726),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          "Upload Image",
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _licenseNumberController.dispose();
    _licenseExpiryController.dispose();
    _policyNumberController.dispose();
    _insuranceExpiryController.dispose();
    _insuranceAmountController.dispose();
    _aadharNumberController.dispose();
    super.dispose();
  }
}
