import 'package:flutter/material.dart';

class ProfileScreen extends StatefulWidget {
  final Map<String, dynamic> driverData;

  const ProfileScreen({
    Key? key,
    required this.driverData,
  }) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),
          const SizedBox(height: 20),

          // Profile Card
          _buildProfileCard(),
          const SizedBox(height: 20),

          // Personal Information
          _buildPersonalInformation(),
          const SizedBox(height: 20),

          // Vehicle Information
          _buildVehicleInformation(),
          const SizedBox(height: 20),

          // Documents Status
          _buildDocumentsStatus(),
          const SizedBox(height: 20),

          // Settings & Actions
          _buildSettingsActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Text(
          "Profile",
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => _showEditRequestDialog(),
          icon: const Icon(
            Icons.edit,
            color: Color(0xFF002395),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF002395), Color(0xFF1565C0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF002395).withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Image
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.white,
            child: widget.driverData['profileImage'] != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(40),
                    child: Image.asset(
                      'assets/images/profile_placeholder.png',
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                    ),
                  )
                : const Icon(
                    Icons.person,
                    color: Color(0xFF002395),
                    size: 40,
                  ),
          ),
          const SizedBox(height: 16),

          // Driver Name
          Text(
            widget.driverData['driverName'] ?? 'Driver Name',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),

          // Phone Number
          Text(
            widget.driverData['phoneNumber'] ?? '+91 XXXXXXXXXX',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),

          // Status Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.verified,
                  color: Colors.white,
                  size: 16,
                ),
                SizedBox(width: 4),
                Text(
                  "Verified Driver",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInformation() {
    return _buildInfoSection(
      "Personal Information",
      [
        _buildInfoItem("Full Name", widget.driverData['driverName'] ?? 'N/A'),
        _buildInfoItem("Age", widget.driverData['age'] ?? 'N/A'),
        _buildInfoItem("Gender", widget.driverData['gender'] ?? 'N/A'),
        _buildInfoItem("Email", widget.driverData['email'] ?? 'N/A'),
        _buildInfoItem("Address", _getFullAddress()),
        _buildInfoItem("Emergency Contact",
            widget.driverData['emergencyContactName'] ?? 'N/A'),
        _buildInfoItem("Emergency Phone",
            widget.driverData['emergencyContactPhone'] ?? 'N/A'),
      ],
    );
  }

  Widget _buildVehicleInformation() {
    return _buildInfoSection(
      "Vehicle Information",
      [
        _buildInfoItem(
            "Vehicle Type", widget.driverData['vehicleType'] ?? 'N/A'),
        _buildInfoItem(
            "Vehicle Number", widget.driverData['vehicleNumber'] ?? 'N/A'),
        _buildInfoItem(
            "License Number", widget.driverData['licenseNumber'] ?? 'N/A'),
        _buildInfoItem(
            "License Expiry", widget.driverData['licenseExpiry'] ?? 'N/A'),
        _buildInfoItem(
            "Insurance Policy", widget.driverData['policyNumber'] ?? 'N/A'),
        _buildInfoItem(
            "Insurance Expiry", widget.driverData['insuranceExpiry'] ?? 'N/A'),
      ],
    );
  }

  Widget _buildDocumentsStatus() {
    return _buildInfoSection(
      "Documents Status",
      [
        _buildDocumentStatusItem(
            "Aadhaar Card", widget.driverData['aadharNumber'] != null),
        _buildDocumentStatusItem(
            "Driving License", widget.driverData['licenseNumber'] != null),
        _buildDocumentStatusItem(
            "PAN Card", widget.driverData['panNumber'] != null),
        _buildDocumentStatusItem(
            "Vehicle RC", widget.driverData['rcDocument'] != null),
        _buildDocumentStatusItem(
            "Insurance", widget.driverData['insuranceDocument'] != null),
        _buildDocumentStatusItem("Police Clearance",
            widget.driverData['policeClearanceDocument'] != null),
      ],
    );
  }

  Widget _buildSettingsActions() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "Settings & Actions",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildActionItem(
            "Edit Profile",
            Icons.edit,
            () => _showEditRequestDialog(),
          ),
          _buildActionItem(
            "Update Documents",
            Icons.file_upload,
            () => _showEditRequestDialog(),
          ),
          _buildActionItem(
            "Change Password",
            Icons.lock,
            () => _showEditRequestDialog(),
          ),
          _buildActionItem(
            "Help & Support",
            Icons.help,
            () => _showComingSoon("Help & Support"),
          ),
          _buildActionItem(
            "Privacy Policy",
            Icons.privacy_tip,
            () => _showComingSoon("Privacy Policy"),
          ),
          _buildActionItem(
            "Logout",
            Icons.logout,
            () => _showLogoutDialog(),
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const Text(
            ": ",
            style: TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentStatusItem(String document, bool isUploaded) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            isUploaded ? Icons.check_circle : Icons.cancel,
            color: isUploaded ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              document,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
          Text(
            isUploaded ? "Uploaded" : "Missing",
            style: TextStyle(
              fontSize: 12,
              color: isUploaded ? Colors.green : Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(String title, IconData icon, VoidCallback onTap,
      {bool isDestructive = false}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(
              icon,
              color: isDestructive ? Colors.red : const Color(0xFF002395),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  color: isDestructive ? Colors.red : Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  String _getFullAddress() {
    final address = widget.driverData['address'] ?? '';
    final city = widget.driverData['city'] ?? '';
    final district = widget.driverData['district'] ?? '';
    final state = widget.driverData['state'] ?? '';
    final pincode = widget.driverData['pincode'] ?? '';

    List<String> addressParts = [];
    if (address.isNotEmpty) addressParts.add(address);
    if (city.isNotEmpty) addressParts.add(city);
    if (district.isNotEmpty) addressParts.add(district);
    if (state.isNotEmpty) addressParts.add(state);
    if (pincode.isNotEmpty) addressParts.add(pincode);

    return addressParts.isNotEmpty ? addressParts.join(', ') : 'N/A';
  }

  void _showEditRequestDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Color(0xFF002395),
              ),
              SizedBox(width: 8),
              Text(
                "Edit Request",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: const Text(
            "Request sent! You will be notified shortly.\n\nOur team will review your edit request and get back to you within 24-48 hours.",
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              height: 1.5,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                "OK",
                style: TextStyle(
                  color: Color(0xFF002395),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            "Logout",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: const Text(
            "Are you sure you want to logout?",
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                "Cancel",
                style: TextStyle(
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate back to welcome screen
                Navigator.of(context).pushNamedAndRemoveUntil(
                  '/',
                  (route) => false,
                );
              },
              child: const Text(
                "Logout",
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature feature coming soon!'),
        backgroundColor: const Color(0xFF002395),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
