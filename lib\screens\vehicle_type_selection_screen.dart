import 'package:flutter/material.dart';
import 'own_vehicle_screen.dart';
import 'company_vehicle_screen.dart';
import 'lease_rented_vehicle_screen.dart';

class VehicleTypeSelectionScreen extends StatefulWidget {
  final String driverName;
  final String phoneNumber;

  const VehicleTypeSelectionScreen({
    super.key,
    required this.driverName,
    required this.phoneNumber,
  });

  @override
  State<VehicleTypeSelectionScreen> createState() =>
      _VehicleTypeSelectionScreenState();
}

class _VehicleTypeSelectionScreenState
    extends State<VehicleTypeSelectionScreen> {
  String? _selectedVehicleType;

  void _navigateToVehicleScreen() {
    if (_selectedVehicleType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a vehicle type'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Widget destinationScreen;
    switch (_selectedVehicleType) {
      case 'own':
        destinationScreen = OwnVehicleScreen(
          driverName: widget.driverName,
          phoneNumber: widget.phoneNumber,
        );
        break;
      case 'company':
        destinationScreen = CompanyVehicleScreen(
          driverName: widget.driverName,
          phoneNumber: widget.phoneNumber,
        );
        break;
      case 'lease':
        destinationScreen = LeaseRentedVehicleScreen(
          driverName: widget.driverName,
          phoneNumber: widget.phoneNumber,
        );
        break;
      default:
        return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => destinationScreen),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF002395),
      appBar: AppBar(
        backgroundColor: const Color(0xFF002395),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Vehicle Ownership Information',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              // Header Section
              Center(
                child: Column(
                  children: [
                    Image.asset(
                      "assets/images/logo.png",
                      height: 50,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      "NIKKOU",
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 1.2,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              const Text(
                "Vehicle Ownership Information",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                "Please select the type of vehicle ownership that applies to you to provide the relevant details.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 30),

              const Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Select Vehicle Type",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Vehicle Type Options
              _buildVehicleTypeOption(
                'own',
                'Own Vehicle',
                'Owned by your individual driver profile details.',
                Icons.directions_car,
              ),
              const SizedBox(height: 16),

              _buildVehicleTypeOption(
                'company',
                'Company Vehicle (Inhouse)',
                'Provide company GST, CIN, PAN, and director details.',
                Icons.business,
              ),
              const SizedBox(height: 16),

              _buildVehicleTypeOption(
                'lease',
                'Lease/Rented Vehicle',
                'Enter owner details and validate their GST information.',
                Icons.assignment,
              ),
              const SizedBox(height: 30),

              // Continue Button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFFFA726),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 2,
                  ),
                  onPressed: _navigateToVehicleScreen,
                  child: const Text(
                    "Continue",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVehicleTypeOption(
      String value, String title, String description, IconData icon) {
    final bool isSelected = _selectedVehicleType == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedVehicleType = value;
        });
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? const Color(0xFFFFA726) : Colors.transparent,
            width: 2,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFFFA726),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: Color(0xFFFFA726),
                size: 24,
              ),
          ],
        ),
      ),
    );
  }
}
