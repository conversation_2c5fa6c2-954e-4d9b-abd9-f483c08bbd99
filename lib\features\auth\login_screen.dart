// Login screen will be defined here
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_typography.dart';
import '../../core/widgets/app_button.dart';
import '../../core/widgets/app_text_field.dart';
// import 'otp_screen.dart';

/// Login screen where user enters their phone number for OTP verification.
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  // Controller for phone number input
  final TextEditingController _phoneController = TextEditingController();

  // Validation flag for phone number
  bool _isValidNumber = false;

  /// Validates the phone number format (10 digits only).
  void _validatePhone(String value) {
    setState(() {
      _isValidNumber = RegExp(r'^[0-9]{10}$').hasMatch(value);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 60),

              /// App logo
              Image.asset('assets/icons/logo.png', height: 80, width: 80),

              const SizedBox(height: 30),

              /// Truck illustration
              Image.asset('assets/icons/truck.png', height: 160, width: 200),

              const SizedBox(height: 12),

              /// Welcome title
              const Text(
                "Welcome",
                textAlign: TextAlign.center,
                style: AppTypography.h1,
              ),

              const SizedBox(height: 30),

              /// Instruction text
              const Text(
                "Enter your phone number. We'll send an OTP to verify your number.",
                textAlign: TextAlign.center,
                style: AppTypography.body1,
              ),

              const SizedBox(height: 40),

              /// Phone input field (reusable component)
              AppTextField(
                controller: _phoneController,
                hintText: "Enter phone number",
                isValid: _isValidNumber,
                onChanged: _validatePhone,
                height: 55,
                width: 380,
                margin: const EdgeInsets.symmetric(vertical: 10),
                textStyle: AppTypography.input.copyWith(fontSize: 16),
                hintStyle: AppTypography.hint.copyWith(fontSize: 14),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                borderRadius: 12,
                keyboardType: TextInputType.phone,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(10),
                ],
                prefix: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: const [
                    Text("🇮🇳", style: TextStyle(fontSize: 20)),
                    SizedBox(width: 10),
                    Text("+91", style: AppTypography.body1),
                    SizedBox(width: 10),
                    VerticalDivider(
                      width: 10,
                      thickness: 1,
                      color: AppColors.border,
                    ),
                    SizedBox(width: 10),
                  ],
                ),
              ),

              const SizedBox(height: 25),

              /// Continue button (enabled only if phone is valid)
              AppButton(
                label: "Continue",
                textStyle: AppTypography.h3,
                isEnabled: _isValidNumber,
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => OtpVerificationScreen(
                        phoneNumber: _phoneController.text,
                      ),
                    ),
                  );
                },
                width: 350,
                height: 48,
                borderRadius: 10,
              ),

              const SizedBox(height: 20),

              /// Terms & Privacy note
              const Text(
                "By continuing, you agree to our Terms & Privacy Policy",
                textAlign: TextAlign.center,
                style: AppTypography.label,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// OTP verification screen (navigated after phone number submission).
class OtpVerificationScreen extends StatelessWidget {
  final String phoneNumber;

  const OtpVerificationScreen({super.key, required this.phoneNumber});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Center(
        child: Text("OTP for: $phoneNumber", style: AppTypography.body1),
      ),
    );
  }
}

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';

// import '../../core/theme/app_colors.dart';
// import '../../core/theme/app_typography.dart';

// import 'otp_screen.dart'; // ✅ import OTP screen

// class LoginScreen extends StatefulWidget {
//   const LoginScreen({super.key});

//   @override
//   State<LoginScreen> createState() => _LoginScreenState();
// }

// class _LoginScreenState extends State<LoginScreen> {
//   final TextEditingController _phoneController = TextEditingController();
//   bool _isValidNumber = false;

//   void _validatePhone(String value) {
//     setState(() {
//       _isValidNumber = RegExp(r'^[0-9]{10}$').hasMatch(value);
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     final screenHeight = MediaQuery.of(context).size.height;
//     final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

//     return Scaffold(
//       backgroundColor: AppColors.primary,
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
//           child: SingleChildScrollView(
//             physics: const ClampingScrollPhysics(),
//             child: ConstrainedBox(
//               constraints: BoxConstraints(
//                 minHeight: screenHeight - keyboardHeight - 100,
//               ),
//               child: IntrinsicHeight(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     SizedBox(height: screenHeight > 700 ? 80 : 40),

//                     /// App Logo with NIKKOU text
//                     Column(
//                       children: [
//                         Image.asset(
//                           'assets/icons/logo.png',
//                           height: screenHeight > 700 ? 60 : 50,
//                           width: screenHeight > 700 ? 60 : 50,
//                           errorBuilder: (context, error, stackTrace) =>
//                               Container(
//                                 height: screenHeight > 700 ? 60 : 50,
//                                 width: screenHeight > 700 ? 60 : 50,
//                                 decoration: BoxDecoration(
//                                   color: AppColors.secondary,
//                                   borderRadius: BorderRadius.circular(8),
//                                 ),
//                                 child: const Icon(
//                                   Icons.business,
//                                   color: Colors.white,
//                                   size: 30,
//                                 ),
//                               ),
//                         ),
//                         const SizedBox(height: 8),
//                         Text(
//                           "NIKKOU",
//                           style: AppTypography.h2.copyWith(
//                             color: AppColors.textOnPrimary,
//                             fontWeight: FontWeight.bold,
//                             letterSpacing: 2,
//                           ),
//                         ),
//                       ],
//                     ),

//                     SizedBox(height: screenHeight > 700 ? 60 : 40),

//                     /// Truck Illustration
//                     Image.asset(
//                       'assets/icons/truck.png',
//                       height: screenHeight > 700 ? 140 : 100,
//                       width: screenHeight > 700 ? 180 : 130,
//                       errorBuilder: (context, error, stackTrace) => Container(
//                         height: screenHeight > 700 ? 140 : 100,
//                         width: screenHeight > 700 ? 180 : 130,
//                         decoration: BoxDecoration(
//                           color: AppColors.surface.withValues(alpha: 0.1),
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                         child: const Icon(
//                           Icons.local_shipping,
//                           color: AppColors.textOnPrimary,
//                           size: 60,
//                         ),
//                       ),
//                     ),

//                     SizedBox(height: screenHeight > 700 ? 40 : 30),

//                     /// Welcome
//                     Text(
//                       "Welcome",
//                       style: AppTypography.h1.copyWith(
//                         color: AppColors.textOnPrimary,
//                         fontSize: 32,
//                         fontWeight: FontWeight.bold,
//                       ),
//                       textAlign: TextAlign.center,
//                     ),

//                     const SizedBox(height: 16),

//                     /// Instruction
//                     Text(
//                       "Enter your phone number. We'll send an OTP to verify\nyour number.",
//                       style: AppTypography.body1.copyWith(
//                         color: AppColors.textOnPrimary.withValues(alpha: 0.9),
//                         fontSize: 16,
//                       ),
//                       textAlign: TextAlign.center,
//                     ),

//                     SizedBox(height: screenHeight > 700 ? 50 : 40),

//                     /// Phone Input
//                     Container(
//                       padding: const EdgeInsets.symmetric(
//                         horizontal: 16,
//                         vertical: 14,
//                       ),
//                       decoration: BoxDecoration(
//                         color: AppColors.surface.withValues(alpha: 0.15),
//                         borderRadius: BorderRadius.circular(12),
//                         border: Border.all(
//                           color: AppColors.secondary.withValues(alpha: 0.8),
//                           width: 1.5,
//                         ),
//                       ),
//                       child: Row(
//                         children: [
//                           /// Country Code
//                           Container(
//                             padding: const EdgeInsets.symmetric(
//                               horizontal: 10,
//                               vertical: 6,
//                             ),
//                             decoration: BoxDecoration(
//                               color: AppColors.secondary.withValues(alpha: 0.2),
//                               borderRadius: BorderRadius.circular(8),
//                             ),
//                             child: Row(
//                               mainAxisSize: MainAxisSize.min,
//                               children: [
//                                 const Text(
//                                   "🇮🇳",
//                                   style: TextStyle(fontSize: 18),
//                                 ),
//                                 const SizedBox(width: 6),
//                                 Text(
//                                   "+91",
//                                   style: AppTypography.body1.copyWith(
//                                     fontWeight: FontWeight.w600,
//                                     color: AppColors.textOnPrimary,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                           const SizedBox(width: 12),

//                           /// Phone Number Input
//                           Expanded(
//                             child: TextField(
//                               controller: _phoneController,
//                               keyboardType: TextInputType.phone,
//                               inputFormatters: [
//                                 FilteringTextInputFormatter.digitsOnly,
//                                 LengthLimitingTextInputFormatter(10),
//                               ],
//                               onChanged: _validatePhone,
//                               style: AppTypography.input.copyWith(
//                                 color: AppColors.textOnPrimary,
//                                 fontSize: 16,
//                                 fontWeight: FontWeight.w500,
//                               ),
//                               decoration: InputDecoration(
//                                 hintText: "789",
//                                 hintStyle: TextStyle(
//                                   color: AppColors.textOnPrimary.withValues(
//                                     alpha: 0.6,
//                                   ),
//                                   fontSize: 16,
//                                 ),
//                                 border: InputBorder.none,
//                                 contentPadding: EdgeInsets.zero,
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),

//                     const SizedBox(height: 25),

//                     /// Continue Button → Redirects to OTP screen
//                     Container(
//                       width: double.infinity,
//                       height: 50,
//                       decoration: BoxDecoration(
//                         color: AppColors.surface.withValues(alpha: 0.2),
//                         borderRadius: BorderRadius.circular(12),
//                         border: Border.all(
//                           color: AppColors.surface.withValues(alpha: 0.3),
//                           width: 1,
//                         ),
//                       ),
//                       child: ElevatedButton(
//                         onPressed: _isValidNumber
//                             ? () {
//                                 Navigator.push(
//                                   context,
//                                   MaterialPageRoute(
//                                     builder: (context) => OtpVerificationScreen(
//                                       phoneNumber: _phoneController.text,
//                                     ),
//                                   ),
//                                 );
//                               }
//                             : null,
//                         style: ElevatedButton.styleFrom(
//                           backgroundColor: Colors.transparent,
//                           shadowColor: Colors.transparent,
//                           shape: RoundedRectangleBorder(
//                             borderRadius: BorderRadius.circular(12),
//                           ),
//                         ),
//                         child: Text(
//                           "Continue",
//                           style: AppTypography.button.copyWith(
//                             color: AppColors.textOnPrimary,
//                             fontSize: 16,
//                             fontWeight: FontWeight.w600,
//                           ),
//                         ),
//                       ),
//                     ),

//                     const Spacer(),

//                     /// Terms
//                     Padding(
//                       padding: const EdgeInsets.only(bottom: 20),
//                       child: Text(
//                         "By continuing, you agree to our Terms & Privacy Policy",
//                         style: AppTypography.label.copyWith(
//                           color: AppColors.textOnPrimary.withValues(alpha: 0.8),
//                           fontSize: 12,
//                         ),
//                         textAlign: TextAlign.center,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
