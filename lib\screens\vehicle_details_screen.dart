import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'training_screen.dart';

class VehicleDetailsScreen extends StatefulWidget {
  final String driverName;
  final String phoneNumber;
  final String vehicleType;

  const VehicleDetailsScreen({
    super.key,
    required this.driverName,
    required this.phoneNumber,
    required this.vehicleType,
  });

  @override
  State<VehicleDetailsScreen> createState() => _VehicleDetailsScreenState();
}

class _VehicleDetailsScreenState extends State<VehicleDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _vehicleNumberController = TextEditingController();
  final _vehicleTypeController = TextEditingController();
  final _vehicleImageController = TextEditingController();

  File? _vehicleImage;
  File? _insuranceDocument;
  File? _rcDocument;
  final ImagePicker _picker = ImagePicker();

  String? _selectedVehicleType;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _pickImage(String documentType) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        switch (documentType) {
          case 'vehicle':
            _vehicleImage = File(image.path);
            break;
          case 'insurance':
            _insuranceDocument = File(image.path);
            break;
          case 'rc':
            _rcDocument = File(image.path);
            break;
        }
      });
    }
  }

  void _submitVehicleDetails() {
    if (_formKey.currentState!.validate()) {
      // Create driver data map
      Map<String, dynamic> driverData = {
        'driverName': widget.driverName,
        'phoneNumber': widget.phoneNumber,
        'vehicleType': widget.vehicleType,
        'vehicleNumber': _vehicleNumberController.text,
        'vehicleImage': _vehicleImage?.path,
        'insuranceDocument': _insuranceDocument?.path,
        'rcDocument': _rcDocument?.path,
      };

      // Navigate to training screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => TrainingScreen(
            driverData: driverData,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF002395),
      appBar: AppBar(
        backgroundColor: const Color(0xFF002395),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Vehicle Details',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                // Header Section
                Center(
                  child: Column(
                    children: [
                      Image.asset(
                        "assets/images/logo.png",
                        height: 50,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        "NIKKOU",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 1.2,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        "Vehicle Details",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFFA726),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),

                // Vehicle Details Form
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Vehicle Number
                      _buildTextField(
                          "Vehicle Number", _vehicleNumberController,
                          placeholder: "e.g., MH 01 AB 1234", isRequired: true),
                      const SizedBox(height: 20),

                      // Vehicle Type Dropdown
                      _buildDropdownField(),
                      const SizedBox(height: 20),

                      // Vehicle Images Section
                      _buildDocumentSection(
                        "Vehicle Images",
                        "Upload images of your vehicle (front, back, both sides)",
                        "vehicle",
                        _vehicleImage,
                        Icons.directions_car,
                      ),
                      const SizedBox(height: 20),

                      // Insurance Documents Section
                      _buildDocumentSection(
                        "Insurance Documents",
                        "Upload your vehicle's insurance/policy documents",
                        "insurance",
                        _insuranceDocument,
                        Icons.security,
                      ),
                      const SizedBox(height: 20),

                      // RC Status Documents Section
                      _buildDocumentSection(
                        "RC Status Documents",
                        "Upload your Registration Certificate (RC)",
                        "rc",
                        _rcDocument,
                        Icons.description,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFFA726),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 2,
                    ),
                    onPressed: _submitVehicleDetails,
                    child: const Text(
                      "Submit",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(
    String label,
    TextEditingController controller, {
    String? placeholder,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: TextFormField(
            controller: controller,
            style: const TextStyle(color: Colors.black87, fontSize: 14),
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: placeholder,
              hintStyle: const TextStyle(color: Colors.black54, fontSize: 14),
            ),
            validator: isRequired
                ? (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '$label is required';
                    }
                    return null;
                  }
                : null,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Vehicle Type",
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedVehicleType,
              hint: const Text(
                "Select a vehicle type",
                style: TextStyle(color: Colors.black54, fontSize: 14),
              ),
              isExpanded: true,
              items: const [
                DropdownMenuItem(value: "Car", child: Text("Car")),
                DropdownMenuItem(
                    value: "Motorcycle", child: Text("Motorcycle")),
                DropdownMenuItem(value: "Truck", child: Text("Truck")),
                DropdownMenuItem(value: "Bus", child: Text("Bus")),
                DropdownMenuItem(
                    value: "Auto Rickshaw", child: Text("Auto Rickshaw")),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedVehicleType = value;
                  _vehicleTypeController.text = value ?? "";
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentSection(
    String title,
    String description,
    String documentType,
    File? selectedFile,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 12),

        // Upload Area
        GestureDetector(
          onTap: () => _pickImage(documentType),
          child: Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.grey[300]!,
                style: BorderStyle.solid,
              ),
            ),
            child: selectedFile != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      selectedFile,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: 120,
                    ),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.cloud_upload_outlined,
                          color: Colors.grey[600],
                          size: 24,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        "Drag & drop or click to upload",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                      const Text(
                        "PNG, JPG, PDF up to 10MB",
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.black38,
                        ),
                      ),
                    ],
                  ),
          ),
        ),

        // Show selected images preview if multiple images for vehicle
        if (documentType == 'vehicle' && selectedFile != null)
          Padding(
            padding: const EdgeInsets.only(top: 12),
            child: Row(
              children: [
                _buildImagePreview(selectedFile),
                const SizedBox(width: 8),
                _buildImagePreview(null), // Placeholder for additional images
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildImagePreview(File? imageFile) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: imageFile != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                imageFile,
                fit: BoxFit.cover,
              ),
            )
          : Icon(
              Icons.add_photo_alternate_outlined,
              color: Colors.grey[400],
              size: 24,
            ),
    );
  }

  @override
  void dispose() {
    _vehicleNumberController.dispose();
    _vehicleTypeController.dispose();
    _vehicleImageController.dispose();
    super.dispose();
  }
}
