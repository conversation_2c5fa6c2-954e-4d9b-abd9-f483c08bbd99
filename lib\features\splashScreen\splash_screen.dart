import 'package:flutter/material.dart';

class SplashScreenConstants {
  static const Color backgroundColor = Color(0xFF002395);
  static const Duration animationDuration = Duration(seconds: 8);
  static const double truckSize = 160;
  static const double logoSize = 140;
  static const double mapWidthFactor = 0.9;

  // Asset paths
  static const String truckAssetPath = 'assets/icons/truck.png';
  static const String logoAssetPath = 'assets/icons/logo.png';
  static const String mapAssetPath = 'assets/icons/Map.png';

  // Text styles
  static const TextStyle welcomeTextStyle = TextStyle(
    fontSize: 26,
    fontWeight: FontWeight.w300,
    color: Colors.white,
  );
  static const TextStyle companyTextStyle = TextStyle(
    fontSize: 34,
    fontWeight: FontWeight.bold,
    color: Colors.white,
    letterSpacing: 2,
  );
  static const TextStyle taglineTextStyle = TextStyle(
    fontSize: 18,
    color: Colors.white70,
  );
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  late Animation<double> _logoFade;
  late Animation<Offset> _logoSlide;

  late Animation<double> _welcomeFade;
  late Animation<Offset> _welcomeSlide;

  late Animation<double> _companyFade;
  late Animation<Offset> _companySlide;

  late Animation<double> _taglineFade;
  late Animation<Offset> _taglineSlide;

  late Animation<double> _mapFade;
  late Animation<Offset> _mapSlide;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: SplashScreenConstants.animationDuration,
    );
    _initAnimations();
    _controller.forward();

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed && mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (_) => const HomeScreen()),
        );
      }
    });
  }

  void _initAnimations() {
    // Logo
    _logoFade = _fade(0.34, 0.46);
    _logoSlide = _slide(0.34, 0.46);

    // Welcome text
    _welcomeFade = _fade(0.47, 0.55);
    _welcomeSlide = _slide(0.47, 0.55);

    // Company text
    _companyFade = _fade(0.56, 0.64);
    _companySlide = _slide(0.56, 0.64);

    // Tagline
    _taglineFade = _fade(0.65, 0.73);
    _taglineSlide = _slide(0.65, 0.73);

    // Map
    _mapFade = _fade(0.74, 1.0);
    _mapSlide = _slide(0.74, 1.0);
  }

  Animation<double> _fade(double start, double end) {
    return Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(start, end, curve: Curves.easeIn),
      ),
    );
  }

  Animation<Offset> _slide(double start, double end) {
    return Tween<Offset>(begin: const Offset(0, 0.6), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(start, end, curve: Curves.easeOutBack),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: SplashScreenConstants.backgroundColor,
      body: Stack(
        children: [
          _buildTruckAnimation(context, screenWidth),
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Logo
                FadeTransition(
                  opacity: _logoFade,
                  child: SlideTransition(
                    position: _logoSlide,
                    child: Image.asset(
                      SplashScreenConstants.logoAssetPath,
                      width: SplashScreenConstants.logoSize,
                      errorBuilder: (c, e, s) =>
                          _buildPlaceholder(SplashScreenConstants.logoSize),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Welcome
                FadeTransition(
                  opacity: _welcomeFade,
                  child: SlideTransition(
                    position: _welcomeSlide,
                    child: const Text(
                      "Welcome to",
                      style: SplashScreenConstants.welcomeTextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                const SizedBox(height: 10),

                // Company
                FadeTransition(
                  opacity: _companyFade,
                  child: SlideTransition(
                    position: _companySlide,
                    child: const Text(
                      "NIKKOU LOGISTICS",
                      style: SplashScreenConstants.companyTextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                const SizedBox(height: 7),

                // Tagline
                FadeTransition(
                  opacity: _taglineFade,
                  child: SlideTransition(
                    position: _taglineSlide,
                    child: const Text(
                      "Your logistics solution",
                      style: SplashScreenConstants.taglineTextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                const SizedBox(height: 40),

                // Map
                FadeTransition(
                  opacity: _mapFade,
                  child: SlideTransition(
                    position: _mapSlide,
                    child: Image.asset(
                      SplashScreenConstants.mapAssetPath,
                      width: screenWidth * SplashScreenConstants.mapWidthFactor,
                      errorBuilder: (c, e, s) => _buildPlaceholder(
                        screenWidth * SplashScreenConstants.mapWidthFactor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTruckAnimation(BuildContext context, double screenWidth) {
    final truckPosition =
        Tween<double>(
          begin: -SplashScreenConstants.truckSize,
          end: screenWidth + SplashScreenConstants.truckSize,
        ).animate(
          CurvedAnimation(
            parent: _controller,
            curve: const Interval(0.0, 0.3, curve: Curves.easeInOut),
          ),
        );

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        if (_controller.value > 0.3) return const SizedBox.shrink();
        return Positioned(
          left: truckPosition.value,
          top:
              MediaQuery.of(context).size.height / 2 -
              SplashScreenConstants.truckSize / 2,
          child: Image.asset(
            SplashScreenConstants.truckAssetPath,
            width: SplashScreenConstants.truckSize,
            height: SplashScreenConstants.truckSize,
            errorBuilder: (c, e, s) =>
                _buildPlaceholder(SplashScreenConstants.truckSize),
          ),
        );
      },
    );
  }

  Widget _buildPlaceholder(double size) {
    return Container(
      width: size,
      height: size,
      color: Colors.grey[300],
      child: const Icon(Icons.error_outline, color: Colors.grey),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(body: Center(child: Text("Home Screen")));
  }
}
