import 'package:flutter/material.dart';
import 'vehicle_details_screen.dart';

class LeaseRentedVehicleScreen extends StatefulWidget {
  final String driverName;
  final String phoneNumber;

  const LeaseRentedVehicleScreen({
    super.key,
    required this.driverName,
    required this.phoneNumber,
  });

  @override
  State<LeaseRentedVehicleScreen> createState() =>
      _LeaseRentedVehicleScreenState();
}

class _LeaseRentedVehicleScreenState extends State<LeaseRentedVehicleScreen> {
  final _formKey = GlobalKey<FormState>();

  // Company Details Controllers
  final _gstNumberController = TextEditingController();
  final _cinController = TextEditingController();
  final _agencyCompanyNameController = TextEditingController();
  final _directorNameController = TextEditingController();
  final _panController = TextEditingController();

  void _submitLeaseVehicleDetails() {
    if (_formKey.currentState!.validate()) {
      // Navigate to vehicle details screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VehicleDetailsScreen(
            driverName: widget.driverName,
            phoneNumber: widget.phoneNumber,
            vehicleType: "Lease/Rented Vehicle",
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF002395),
      appBar: AppBar(
        backgroundColor: const Color(0xFF002395),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Lease / Rented Vehicle',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                // Header Section
                Center(
                  child: Column(
                    children: [
                      Image.asset(
                        "assets/images/logo.png",
                        height: 50,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        "NIKKOU",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 1.2,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        "Lease / Rented Vehicle",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFFA726),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 30),

                // Owner Profile Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFA726),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            "Owner Profile",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Owner Profile Card - Driver's Details
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 25,
                              backgroundColor: Colors.grey[300],
                              child: const Icon(
                                Icons.person,
                                color: Colors.grey,
                                size: 30,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.driverName,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  Text(
                                    widget.phoneNumber,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.black54,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                // Edit Profile functionality
                              },
                              child: const Text(
                                "Edit Profile",
                                style: TextStyle(
                                  color: Color(0xFF002395),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Company Details Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        "Company Details",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildTextField("GST Number", _gstNumberController,
                          isRequired: true),
                      const SizedBox(height: 16),
                      _buildTextField("CIN (Corporate Identification Number)",
                          _cinController,
                          isRequired: true),
                      const SizedBox(height: 16),
                      _buildTextField(
                          "Agency/Company Name", _agencyCompanyNameController,
                          isRequired: true),
                      const SizedBox(height: 16),
                      _buildTextField(
                          "Director's Name", _directorNameController,
                          isRequired: true),
                      const SizedBox(height: 16),
                      _buildTextField(
                          "PAN (Permanent Account Number)", _panController,
                          isRequired: true),
                    ],
                  ),
                ),
                const SizedBox(height: 30),

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFFA726),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 2,
                    ),
                    onPressed: _submitLeaseVehicleDetails,
                    child: const Text(
                      "Save & Continue",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField(
    String label,
    TextEditingController controller, {
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: TextFormField(
            controller: controller,
            style: const TextStyle(color: Colors.black87, fontSize: 14),
            decoration: const InputDecoration(
              border: InputBorder.none,
            ),
            validator: isRequired
                ? (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '$label is required';
                    }
                    if (label.contains("GST") && value.length != 15) {
                      return 'GST number must be 15 characters';
                    }
                    if (label.contains("PAN") && value.length != 10) {
                      return 'PAN must be 10 characters';
                    }
                    if (label.contains("CIN") && value.length != 21) {
                      return 'CIN must be 21 characters';
                    }
                    return null;
                  }
                : null,
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _gstNumberController.dispose();
    _cinController.dispose();
    _agencyCompanyNameController.dispose();
    _directorNameController.dispose();
    _panController.dispose();
    super.dispose();
  }
}
